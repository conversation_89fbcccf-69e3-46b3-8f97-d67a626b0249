<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>销售数据分析系统</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/import.css">
    <link rel="stylesheet" href="css/charts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <!-- Three.js for 3D bubble charts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

    <!-- 数据验证和监控组件 -->
    <script src="js/data-validator.js"></script>
    <script src="js/debug-monitor.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/data-corrector.js"></script>

    <style>
        /* 销售员名字点击样式 */
        .personnel-name-clickable {
            color: #2563eb !important;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.2s ease;
        }

        .personnel-name-clickable:hover {
            color: #1d4ed8 !important;
            text-decoration: none;
        }

        /* 销售员明细行样式 */
        .personnel-detail-row {
            background-color: #f8f9fa;
        }

        .personnel-detail-container {
            padding: 15px;
            background-color: #f8f9fa;
        }

        /* 明细表格样式 */
        .personnel-detail-container table {
            font-size: 12px;
            margin: 10px 0;
        }

        .personnel-detail-container th {
            background-color: #f3f4f6;
            font-size: 13px;
            padding: 8px;
        }

        .personnel-detail-container td {
            font-size: 13px;
            padding: 8px;
        }

        /* 加载状态样式 */
        .loading-text {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 电脑端顶部导航栏 -->
        <header class="desktop-top-nav" id="desktopTopNav">
            <div class="desktop-nav-left">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span class="logo-text">销售分析</span>
                </div>
            </div>
            <nav class="desktop-nav-center">
                <ul class="desktop-nav-list">
                    <li class="desktop-nav-item active">
                        <a href="#dashboard" class="desktop-nav-link" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li class="desktop-nav-item">
                        <a href="#sales" class="desktop-nav-link" data-page="sales">
                            <i class="fas fa-shopping-cart"></i>
                            <span>销售数据</span>
                        </a>
                    </li>
                    <li class="desktop-nav-item admin-only" style="display: none;">
                        <a href="#import" class="desktop-nav-link" data-page="import">
                            <i class="fas fa-upload"></i>
                            <span>数据导入</span>
                        </a>
                    </li>
                    <li class="desktop-nav-item admin-only" style="display: none;">
                        <a href="#users" class="desktop-nav-link" data-page="users">
                            <i class="fas fa-users"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li class="desktop-nav-item">
                        <a href="#settings" class="desktop-nav-link" data-page="settings">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="desktop-nav-right">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name" id="desktopUserName">加载中...</div>
                        <div class="user-role" id="desktopUserRole">用户</div>
                    </div>
                </div>
                <button class="logout-btn" id="desktopLogoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出</span>
                </button>
            </div>
        </header>

        <!-- 移动端侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <span class="logo-text">销售分析</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item active">
                    <a href="#dashboard" class="nav-link" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#sales" class="nav-link" data-page="sales">
                        <i class="fas fa-shopping-cart"></i>
                        <span>销售数据</span>
                    </a>
                </li>
                <li class="nav-item admin-only" style="display: none;">
                    <a href="#import" class="nav-link" data-page="import">
                        <i class="fas fa-upload"></i>
                        <span>数据导入</span>
                    </a>
                </li>
                <li class="nav-item admin-only" style="display: none;">
                    <a href="#users" class="nav-link" data-page="users">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" id="userName">加载中...</div>
                    <div class="user-role" id="userRole">用户</div>
                </div>
            </div>
            <button class="logout-btn" id="logoutBtn">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <header class="top-header">
            <div class="header-left">
                <button class="hamburger-btn" id="hamburgerBtn">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <h1 class="page-title" id="pageTitle">仪表盘</h1>
            </div>
            <div class="header-right">
                <!-- 右上角按钮已移除 -->
            </div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content" id="pageContent">
            <!-- 仪表盘内容 -->
            <div class="content-section" id="dashboard-content">
                <!-- 仪表盘内容已清空 -->
            </div>

            <!-- 销售数据内容 -->
            <div class="content-section" id="sales-content">

                <!-- 筛选器 -->
                <div class="filters-container">
                    <div class="filter-row">
                        <!-- 1. 月份（多选） - 第一位 -->
                        <div class="filter-group">
                            <label>月份（多选）</label>
                            <div class="multi-select" id="monthMultiSelect">
                                <div class="multi-select-display" id="monthDisplay">
                                    <span class="placeholder">选择月份</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="multi-select-dropdown" id="monthDropdown">
                                    <div class="select-all-option">
                                        <label>
                                            <input type="checkbox" id="selectAllMonths">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                    <div class="options-list" id="monthOptions"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 2. 销售员（多选） - 第二位 -->
                        <div class="filter-group" id="repFilterGroup">
                            <label>销售员（多选）</label>
                            <div class="multi-select" id="repMultiSelect">
                                <div class="multi-select-display" id="repDisplay">
                                    <span class="placeholder">选择销售员</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="multi-select-dropdown" id="repDropdown">
                                    <div class="select-all-option">
                                        <label>
                                            <input type="checkbox" id="selectAllReps">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                    <div class="options-list" id="repOptions"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 3. 地区产品线（多选） - 第三位 -->
                        <div class="filter-group">
                            <label>地区产品线（多选）</label>
                            <div class="multi-select" id="regionProductLineMultiSelect">
                                <div class="multi-select-display" id="regionProductLineDisplay">
                                    <span class="placeholder">选择地区产品线</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="multi-select-dropdown" id="regionProductLineDropdown">
                                    <div class="select-all-option">
                                        <label>
                                            <input type="checkbox" id="selectAllRegionProductLines">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                    <div class="options-list" id="regionProductLineOptions"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 4. 产品（多选） - 第四位 -->
                        <div class="filter-group">
                            <label>产品（多选）</label>
                            <div class="multi-select" id="productMultiSelect">
                                <div class="multi-select-display" id="productDisplay">
                                    <span class="placeholder">选择产品</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="multi-select-dropdown" id="productDropdown">
                                    <div class="select-all-option">
                                        <label>
                                            <input type="checkbox" id="selectAllProducts">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                    <div class="options-list" id="productOptions"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 5. 医院（多选） - 第五位 -->
                        <div class="filter-group">
                            <label>医院（多选）</label>
                            <div class="multi-select" id="terminalMultiSelect">
                                <div class="multi-select-display" id="terminalDisplay">
                                    <span class="placeholder">选择医院</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="multi-select-dropdown" id="terminalDropdown">
                                    <div class="select-all-option">
                                        <label>
                                            <input type="checkbox" id="selectAllTerminals">
                                            <span>全选</span>
                                        </label>
                                    </div>
                                    <div class="options-list" id="terminalOptions"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-group">
                            <button class="btn btn-primary" id="searchBtn">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <button class="btn btn-secondary" id="resetBtn">
                                <i class="fas fa-refresh"></i>
                                重置
                            </button>
                            <button class="btn btn-warning" onclick="testPersonnelTableDirect()" style="margin-left: 10px;">
                                <i class="fas fa-test"></i>
                                测试销售员表格
                            </button>
                        </div>
                    </div>
                </div>



                <!-- 销售分析内容 -->
                <div class="sales-analysis-container">
                    <!-- 产品销售分析表格 -->
                    <div class="analysis-section">
                        <div class="section-header">
                            <button class="toggle-btn" id="toggleProductAnalysis"
                                    onclick="toggleSection('productAnalysisContent', 'toggleProductAnalysis')">
                                <span class="toggle-text">折叠</span>
                            </button>
                            <h3>产品销售完成数据分析表</h3>
                        </div>
                        <div class="collapsible-content" id="productAnalysisContent">
                            <div class="analysis-table-wrapper">
                                <table class="analysis-table" id="productAnalysisTable">
                                    <thead>
                                        <tr>
                                            <th>产品</th>
                                            <th>实际销售</th>
                                            <th>指标</th>
                                            <th>达成率</th>
                                            <th>同期销售</th>
                                            <th>同期增长</th>
                                            <th>环比销售</th>
                                            <th>环比增长</th>
                                        </tr>
                                    </thead>
                                    <tbody id="productAnalysisBody">
                                        <tr>
                                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">请选择查询条件并点击搜索</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="charts-section" id="chartsSection" style="display: none;">
                        <div class="section-header">
                            <button class="toggle-btn" id="toggleChartsContainer"
                                    onclick="toggleSection('chartsContainerContent', 'toggleChartsContainer')">
                                <span class="toggle-text">折叠</span>
                            </button>
                            <h3>数据分析图表</h3>
                        </div>
                        <div class="collapsible-content" id="chartsContainerContent">
                            <div class="charts-container" id="chartsContainer">
                                <!-- 总达成率环形图 -->
                                <div class="chart-item">
                                    <h3>总达成率</h3>
                                    <div class="achievement-chart-container">
                                        <canvas id="achievementDonutChart" width="300" height="300"></canvas>
                                        <div class="chart-center-text" id="achievementCenterText">
                                            <div class="achievement-rate">0%</div>
                                            <div class="achievement-label">达成率</div>
                                        </div>
                                    </div>
                                </div>



                        <!-- 同期增长率环形图 -->
                        <div class="chart-item">
                            <h3>同期增长率</h3>
                            <div class="yoy-comparison-chart-container">
                                <canvas id="yoyComparisonChart" width="300" height="300"></canvas>
                                <div class="chart-center-text" id="yoyComparisonCenterText">
                                    <div class="comparison-ratio">0%</div>
                                    <div class="comparison-label">同期增长</div>
                                </div>
                            </div>
                            <div class="chart-data-info" id="yoyDataInfo">
                                <div class="data-item">
                                    <span class="data-label">本期:</span>
                                    <span class="data-value" id="yoyCurrentValue">¥0</span>
                                </div>
                                <div class="data-item">
                                    <span class="data-label">同期:</span>
                                    <span class="data-value" id="yoyPreviousValue">¥0</span>
                                </div>
                            </div>
                        </div>

                        <!-- 环比增长率环形图 -->
                        <div class="chart-item">
                            <h3>环比增长率</h3>
                            <div class="mom-comparison-chart-container">
                                <canvas id="momComparisonChart" width="300" height="300"></canvas>
                                <div class="chart-center-text" id="momComparisonCenterText">
                                    <div class="comparison-ratio">0%</div>
                                    <div class="comparison-label">环比增长</div>
                                </div>
                            </div>
                            <div class="chart-data-info" id="momDataInfo">
                                <div class="data-item">
                                    <span class="data-label">当期:</span>
                                    <span class="data-value" id="momCurrentValue">¥0</span>
                                </div>
                                <div class="data-item">
                                    <span class="data-label">上期:</span>
                                    <span class="data-value" id="momPreviousValue">¥0</span>
                                </div>
                            </div>
                        </div>

                        <!-- 产品占比饼图 -->
                        <div class="chart-item chart-item-pie-large">
                            <h3>产品销售占比</h3>
                            <canvas id="productShareChart" width="400" height="400"></canvas>
                        </div>

                        <!-- 销售员占比分析图 - 仅管理员可见 -->
                        <div class="chart-item chart-item-pie-large" id="pieChartItem" style="display: none;">
                            <h3>销售员占比</h3>
                            <canvas id="doublePieChart" width="400" height="400"></canvas>
                        </div>

                            </div>
                        </div>
                    </div>

                    <!-- 月份对比图表区域 -->
                    <div class="monthly-charts-section" id="monthlyChartsSection" style="display: none;">
                        <div class="section-header">
                            <button class="toggle-btn" id="toggleMonthlyCharts"
                                    onclick="toggleSection('monthlyChartsContent', 'toggleMonthlyCharts')">
                                <span class="toggle-text">折叠</span>
                            </button>
                            <h3>月份对比分析图表</h3>
                        </div>
                        <div class="collapsible-content" id="monthlyChartsContent">
                            <div class="monthly-charts-container" id="monthlyChartsContainer">
                                <!-- 月份同期对比柱状图 -->
                                <div class="chart-item chart-item-half" id="monthlyComparisonChartItem">
                                    <h3>产品同期销售额对比</h3>
                                    <div class="monthly-comparison-chart-container">
                                        <canvas id="monthlyComparisonChart" width="800" height="400"></canvas>
                                    </div>
                                </div>

                                <!-- 月份环比增长柱状图 -->
                                <div class="chart-item chart-item-half" id="monthlyMomChartItem">
                                    <h3>产品环比销售额对比</h3>
                                    <div class="monthly-comparison-chart-container">
                                        <canvas id="monthlyMomChart" width="800" height="400"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 医院销量占比分析图表区域 -->
                    <div class="hospital-sales-chart-section" id="hospitalSalesChartSection" style="display: none;">
                        <div class="section-header">
                            <button class="toggle-btn" id="toggleHospitalSalesChart"
                                    onclick="toggleSection('hospitalSalesChartContent', 'toggleHospitalSalesChart')">
                                <span class="toggle-text">折叠</span>
                            </button>
                        </div>
                        <div class="collapsible-content" id="hospitalSalesChartContent">
                            <div class="hospital-sales-chart-container" id="hospitalSalesChartContainer">
                                <!-- 医院同期对比柱状图 -->
                                <div class="chart-item chart-item-half" id="hospitalComparisonChartItem">
                                    <div class="monthly-comparison-chart-container">
                                        <canvas id="hospitalComparisonChart" width="800" height="400"></canvas>
                                    </div>
                                </div>

                                <!-- 医院环比增长柱状图 -->
                                <div class="chart-item chart-item-half" id="hospitalMomChartItem">
                                    <div class="monthly-comparison-chart-container">
                                        <canvas id="hospitalMomChart" width="800" height="400"></canvas>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                        <!-- 销售指标对比图表和表格 -->
                        <div class="sales-target-comparison-section" id="salesTargetComparisonSection" style="display: none;">
                            <div class="section-header">
                                <button class="toggle-btn" id="toggleSalesTargetComparison"
                                        onclick="toggleSection('salesTargetComparisonContent', 'toggleSalesTargetComparison')">
                                    <span class="toggle-text">折叠</span>
                                </button>
                                <h3>销售指标对比分析</h3>
                            </div>
                            <div class="collapsible-content" id="salesTargetComparisonContent">
                                <div class="sales-target-comparison-container" id="salesTargetComparisonContainer">
                                    <div class="chart-header">
                                        <div class="chart-controls">
                                            <select id="salesTargetDisplayMode" class="form-select">
                                                <option value="amount">销售金额折算后 vs 指标金额</option>
                                                <option value="quantity">销量盒折算后 vs 指标盒折算后</option>
                                            </select>
                                        </div>
                                    </div>

                            <!-- 图表容器 -->
                            <div class="chart-container">
                                <canvas id="salesTargetComparisonChart" width="800" height="400"></canvas>
                            </div>

                            <!-- 数据表格 -->
                            <div class="sales-target-table-container">
                                <div class="section-header">
                                    <button class="toggle-btn" id="toggleSalesTargetTable"
                                            onclick="toggleSection('salesTargetTableContent', 'toggleSalesTargetTable')">
                                        <span class="toggle-text">折叠</span>
                                    </button>
                                    <h4>销售指标详情数据</h4>
                                </div>
                                <div class="collapsible-content" id="salesTargetTableContent">
                                    <div class="table-responsive">
                                        <table class="sales-target-table" id="salesTargetTable">
                                            <thead>
                                                <tr>
                                                    <th>月份</th>
                                                    <th id="salesColumnHeader">销售金额折算后</th>
                                                    <th id="targetColumnHeader">指标金额</th>
                                                    <th>销量盒折算后</th>
                                                    <th>指标盒折算后</th>
                                                    <th>完成率</th>
                                                    <th>QP值</th>
                                                </tr>
                                            </thead>
                                            <tbody id="salesTargetTableBody">
                                                <!-- 数据行将通过JavaScript动态生成 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                                </div>
                            </div>
                        </div>

                        <!-- 人员和产品气泡图区域 -->
                        <div class="bubble-charts-section" id="bubbleChartsSection" style="display: none;">
                            <div class="section-header">
                                <button class="toggle-btn" id="toggleBubbleCharts"
                                        onclick="toggleSection('bubbleChartsContent', 'toggleBubbleCharts')">
                                    <span class="toggle-text">折叠</span>
                                </button>
                                <h3>人员和产品气泡图分析</h3>
                            </div>
                            <div class="collapsible-content" id="bubbleChartsContent">
                                <div class="bubble-charts-container" id="bubbleChartsContainer">
                                    <div class="bubble-charts-header">
                                        <p class="bubble-charts-description">以金额、达成率(QP)、增长率(GR)三个维度展示人员和产品表现</p>
                                        <div class="chart-mode-toggle">
                                            <button id="toggle2D" class="mode-btn active">2D视图</button>
                                            <button id="toggle3D" class="mode-btn">3D视图</button>
                                        </div>
                                    </div>

                            <!-- 双气泡图并排布局 -->
                            <div class="dual-bubble-layout">
                                <!-- 人员气泡图 -->
                                <div class="bubble-chart-container" id="personnelBubbleContainer">
                                    <div class="chart-header">
                                        <h4>人员表现气泡图</h4>
                                        <div class="bubble-legend">
                                            <div class="legend-item">
                                                <span class="legend-label">X轴: 达成率(QP)</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-label">Y轴: 增长率(GR)</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-label">气泡大小: 销售金额</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chart-canvas-container">
                                        <canvas id="personnelBubbleChart" style="display: none;"></canvas>
                                        <div id="personnel3DBubbleChart" class="threejs-container"></div>
                                    </div>
                                </div>

                                <!-- 产品气泡图 -->
                                <div class="bubble-chart-container" id="productBubbleContainer">
                                    <div class="chart-header">
                                        <h4>产品表现气泡图</h4>
                                        <div class="bubble-legend">
                                            <div class="legend-item">
                                                <span class="legend-label">X轴: 达成率(QP)</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-label">Y轴: 增长率(GR)</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-label">气泡大小: 销售金额</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chart-canvas-container">
                                        <canvas id="productBubbleChart" style="display: none;"></canvas>
                                        <div id="product3DBubbleChart" class="threejs-container"></div>
                                    </div>
                                </div>
                            </div>
                                </div>
                            </div>
                        </div>

                    </div>



                    <!-- 动态分析表格 -->
                    <div class="collapsible-section">
                        <div class="section-header">
                            <h3>
                                <button class="toggle-btn" id="toggleDynamicAnalysis"
                                        onclick="toggleSection('dynamicAnalysisContent', 'toggleDynamicAnalysis')">
                                    <span class="toggle-text">折叠</span>
                                </button>
                                多维度分析表
                            </h3>
                        </div>
                        <div class="collapsible-content" id="dynamicAnalysisContent">
                            <!-- 分析维度选择器 -->
                            <div class="analysis-dimension-selector" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                                <div style="margin-bottom: 15px;">
                                    <div style="font-weight: 600; color: #374151; margin-bottom: 10px;">层级分析维度（按顺序选择）：</div>
                                    <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span style="font-weight: 500; color: #6b7280;">第1层：</span>
                                            <select id="dimension1" onchange="updateDimensionHierarchy()" style="padding: 5px 10px; border: 1px solid #d1d5db; border-radius: 4px;">
                                                <option value="">请选择</option>
                                                <option value="product">产品</option>
                                                <option value="personnel">销售员</option>
                                                <option value="month">月份</option>
                                                <option value="terminal">医院</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span style="font-weight: 500; color: #6b7280;">第2层：</span>
                                            <select id="dimension2" onchange="updateDimensionHierarchy()" style="padding: 5px 10px; border: 1px solid #d1d5db; border-radius: 4px;">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span style="font-weight: 500; color: #6b7280;">第3层：</span>
                                            <select id="dimension3" onchange="updateDimensionHierarchy()" style="padding: 5px 10px; border: 1px solid #d1d5db; border-radius: 4px;">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span style="font-weight: 500; color: #6b7280;">第4层：</span>
                                            <select id="dimension4" onchange="updateDimensionHierarchy()" style="padding: 5px 10px; border: 1px solid #d1d5db; border-radius: 4px;">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                                    <button onclick="refreshDynamicAnalysis()"
                                            style="padding: 8px 16px; background-color: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                        生成分析
                                    </button>
                                    <button onclick="clearDimensionHierarchy()"
                                            style="padding: 8px 16px; background-color: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                        清空选择
                                    </button>
                                </div>
                                <div id="dimensionHint" style="margin-top: 10px; font-size: 14px; color: #6b7280;">
                                    请按层级顺序选择分析维度，系统将生成层级结构的分析表格
                                </div>
                            </div>

                            <!-- 层级说明 -->
                            <div id="hierarchyLegend" style="display: none; margin-bottom: 15px; padding: 12px; background-color: #f0f9ff; border: 1px solid #bfdbfe; border-radius: 6px;">
                                <div style="font-weight: 600; color: #1e40af; margin-bottom: 8px;">层级图例：</div>
                                <div style="display: flex; gap: 20px; flex-wrap: wrap; font-size: 14px;">
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <span style="font-family: monospace; font-weight: bold; color: #374151;">[+]</span>
                                        <span style="color: #6b7280;">第1层（可展开）</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <span style="font-family: monospace; font-weight: bold; color: #374151;">[-]</span>
                                        <span style="color: #6b7280;">第1层（已展开）</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <span style="font-family: monospace; font-weight: bold; color: #374151;">▶</span>
                                        <span style="color: #6b7280;">第2层（可展开）</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <span style="font-family: monospace; font-weight: bold; color: #374151;">▼</span>
                                        <span style="color: #6b7280;">第2层（已展开）</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <span style="font-family: monospace; font-weight: bold; color: #374151;">○</span>
                                        <span style="color: #6b7280;">第3层（可展开）</span>
                                    </div>
                                </div>
                            </div>

                            <div class="analysis-table-wrapper">
                                <table class="analysis-table" id="dynamicAnalysisTable">
                                    <thead id="dynamicAnalysisHead">
                                        <tr>
                                            <th>请选择分析维度</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dynamicAnalysisBody">
                                        <tr><td style="text-align: center; padding: 40px; color: #6b7280;">请在上方选择分析维度</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>


                    <!-- 医院销售对比表格区域 -->
                    <div class="hospital-comparison-table-section" id="hospitalComparisonTableSection">
                        <div class="section-header">
                            <button class="toggle-btn" id="toggleHospitalComparisonTable"
                                    onclick="toggleSection('hospitalComparisonTableContent', 'toggleHospitalComparisonTable')">
                                <span class="toggle-text">折叠</span>
                            </button>
                            <h3>医院销售对比表格</h3>
                        </div>
                        <div class="collapsible-content" id="hospitalComparisonTableContent">
                            <div class="hospital-comparison-table-container" id="hospitalComparisonTableContainer">
                                <div class="table-controls">
                                    <div class="control-group">
                                        <button id="loadHospitalComparisonData" class="btn btn-primary">根据当前筛选条件加载对比数据</button>
                                    </div>
                                </div>

                                <!-- 医院销售对比表格 -->

                                <!-- 80%医院分组 -->
                                <div class="hospital-group-section">
                                    <div class="hospital-group-header" onclick="toggleHospitalGroup('hospitals80')">
                                        <span class="toggle-icon" id="hospitals80Icon">+</span>
                                        <span class="group-title">80%医院分组</span>
                                        <span class="group-stats">
                                            (<span id="hospitals80Count">0</span>家医院,
                                            <span id="hospitals80Sales">0</span>万元)
                                        </span>
                                    </div>
                                    <div class="hospital-group-content" id="hospitals80Content" style="display: none;">
                                        <div class="table-responsive">
                                            <table class="hospital-comparison-table" id="hospitals80Table">
                                                <thead id="hospitals80TableHead">
                                                    <!-- 三层表头将通过JavaScript动态生成 -->
                                                </thead>
                                                <tbody id="hospitals80TableBody">
                                                    <!-- 数据行将通过JavaScript动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- 20%医院分组 -->
                                <div class="hospital-group-section">
                                    <div class="hospital-group-header" onclick="toggleHospitalGroup('hospitals20')">
                                        <span class="toggle-icon" id="hospitals20Icon">+</span>
                                        <span class="group-title">20%医院分组</span>
                                        <span class="group-stats">
                                            (<span id="hospitals20Count">0</span>家医院,
                                            <span id="hospitals20Sales">0</span>万元)
                                        </span>
                                    </div>
                                    <div class="hospital-group-content" id="hospitals20Content" style="display: none;">
                                        <div class="table-responsive">
                                            <table class="hospital-comparison-table" id="hospitals20Table">
                                                <thead id="hospitals20TableHead">
                                                    <!-- 三层表头将通过JavaScript动态生成 -->
                                                </thead>
                                                <tbody id="hospitals20TableBody">
                                                    <!-- 数据行将通过JavaScript动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>



                                <!-- 汇总信息 -->
                                <div class="summary-info" id="hospitalComparisonSummary">
                                    <div class="summary-item">
                                        <span class="summary-label">总医院数:</span>
                                        <span class="summary-value" id="totalHospitalsCount">0</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">总销售额:</span>
                                        <span class="summary-value">¥<span id="totalSalesAmount">0</span>万</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- 数据导入内容 -->
            <div class="content-section" id="import-content">
                <div class="import-container">
                    <div class="import-header">
                        <div class="import-header-content">
                            <div class="import-title">
                                <h1><i class="fas fa-upload"></i> 数据导入</h1>
                                <p>支持 Excel (.xlsx, .xls) 和 CSV 文件格式</p>
                            </div>
                            <div class="import-actions">
                                <button class="btn btn-danger" id="clearDataBtn" onclick="showClearDataModal()">
                                    <i class="fas fa-trash-alt"></i>
                                    清空数据表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据管理工具栏 -->
                    <div class="data-management-toolbar" style="display: flex; margin-bottom: 30px; padding: 15px 20px; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); border-left: 4px solid #f59e0b; align-items: center; justify-content: space-between; gap: 15px;">
                        <button class="btn btn-warning" id="cleanRedundantDataDashboard" title="清理销量、销售金额、指标盒数、指标金额都为0的冗余数据" style="background: #f59e0b; color: white; border: none; padding: 10px 16px; border-radius: 8px; cursor: pointer; display: inline-flex; align-items: center; gap: 8px; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-broom"></i>
                            清空冗余数据
                        </button>
                        <div class="toolbar-info" style="flex: 1; text-align: right;">
                            <small style="color: #6b7280; font-size: 12px;">清理条件：销量盒折算后、销售金额折算后、指标盒折算后、指标金额都为0的数据</small>
                        </div>
                    </div>

                    <!-- 步骤指示器 -->
                    <div class="steps-indicator">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-title">上传文件</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-title">数据预览</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-title">确认导入</div>
                        </div>
                    </div>

                    <!-- 步骤1: 文件上传 -->
                    <div class="step-content active" id="step1">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <h3>拖拽文件到此处或点击选择文件</h3>
                                <p>支持 .xlsx, .xls, .csv 格式，最大 10MB</p>
                            </div>
                            <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" style="display: none;">
                            <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open"></i>
                                选择文件
                            </button>
                        </div>

                        <div class="upload-progress" id="uploadProgress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="progress-text" id="progressText">上传中...</div>
                        </div>

                        <div class="file-info" id="fileInfo" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-file-excel"></i>
                                <div class="file-meta">
                                    <div class="file-name" id="fileName"></div>
                                    <div class="file-size" id="fileSize"></div>
                                </div>
                            </div>
                            <button class="btn btn-danger" id="removeFile">
                                <i class="fas fa-trash"></i>
                                移除
                            </button>
                        </div>
                    </div>

                    <!-- 步骤2: 数据预览 -->
                    <div class="step-content" id="step2">
                        <div class="preview-header">
                            <h3>数据预览</h3>
                            <div class="preview-stats" id="previewStats"></div>
                        </div>

                        <div class="preview-table-container">
                            <table class="preview-table" id="previewTable">
                                <thead id="previewTableHead"></thead>
                                <tbody id="previewTableBody"></tbody>
                            </table>
                        </div>

                        <div class="preview-actions">
                            <button class="btn btn-secondary" onclick="goToStep(1)">
                                <i class="fas fa-arrow-left"></i>
                                返回上传
                            </button>
                            <button class="btn btn-primary" onclick="goToStep(3)">
                                确认数据
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 步骤3: 确认导入 -->
                    <div class="step-content" id="step3">
                        <div class="confirm-header">
                            <h3>确认导入</h3>
                            <p>请确认以下信息无误后开始导入</p>
                        </div>

                        <div class="import-summary" id="importSummary">
                            <div class="summary-item">
                                <label>总记录数:</label>
                                <span id="totalRecords">0</span>
                            </div>
                            <div class="summary-item">
                                <label>数据字段:</label>
                                <span id="dataFields"></span>
                            </div>
                            <div class="summary-item">
                                <label>上传人:</label>
                                <span id="uploader"></span>
                            </div>
                        </div>

                        <div class="import-actions">
                            <button class="btn btn-secondary" onclick="goToStep(2)">
                                <i class="fas fa-arrow-left"></i>
                                返回预览
                            </button>
                            <button class="btn btn-success" id="confirmImport">
                                <i class="fas fa-check"></i>
                                开始导入
                            </button>
                        </div>

                        <div class="import-result" id="importResult" style="display: none;">
                            <div class="result-icon" id="resultIcon"></div>
                            <div class="result-message" id="resultMessage"></div>
                            <div class="result-details" id="resultDetails"></div>
                            <div class="result-actions">
                                <button class="btn btn-primary" onclick="showPage('dashboard')">
                                    <i class="fas fa-home"></i>
                                    返回首页
                                </button>
                                <button class="btn btn-secondary" onclick="location.reload()">
                                    <i class="fas fa-refresh"></i>
                                    重新导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户管理内容 -->
            <div class="content-section" id="users-content">
                <div class="users-management-container">
                    <div class="users-header">
                        <button class="btn btn-primary" id="addUserBtn">
                            <i class="fas fa-plus"></i>
                            添加用户
                        </button>
                    </div>

                    <div class="users-table-wrapper">
                        <table class="users-table" id="usersTable">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>姓名</th>
                                    <th>角色</th>
                                    <th>部门</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="8" style="text-align: center; color: #666; padding: 40px;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 设置内容 -->
            <div class="content-section" id="settings-content">
                <h2>设置</h2>
                <p>设置功能正在开发中...</p>
            </div>
        </main>

        <!-- 遮罩层 -->
        <div class="overlay" id="overlay"></div>
    </div>

    <!-- 用户管理模态框 -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">添加用户</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId" name="id">
                    
                    <div class="form-group">
                        <label for="username">用户名 *</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group" id="passwordGroup">
                        <label for="password">密码 *</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="fullName">真实姓名 *</label>
                        <input type="text" id="fullName" name="full_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">邮箱</label>
                        <input type="email" id="email" name="email">
                    </div>
                    
                    <div class="form-group">
                        <label for="role">角色 *</label>
                        <select id="role" name="role" required>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="department">部门</label>
                        <input type="text" id="department" name="department">
                    </div>
                    
                    <div class="form-group" id="statusGroup" style="display: none;">
                        <label for="isActive">状态</label>
                        <select id="isActive" name="is_active">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                <button type="submit" form="userForm" class="btn btn-primary" id="saveUserBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal" id="passwordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>重置密码</h3>
                <button class="modal-close" onclick="closePasswordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="passwordForm">
                    <input type="hidden" id="resetUserId" name="userId">
                    
                    <div class="form-group">
                        <label for="newPassword">新密码 *</label>
                        <input type="password" id="newPassword" name="password" required minlength="6">
                        <small class="form-text">密码长度至少6位</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">确认密码 *</label>
                        <input type="password" id="confirmPassword" required minlength="6">
                        <small class="form-text">请再次输入新密码</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePasswordModal()">取消</button>
                <button type="submit" form="passwordForm" class="btn btn-primary">重置密码</button>
            </div>
        </div>
    </div>

    <!-- 清空数据确认模态框 -->
    <div class="modal" id="clearDataModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> 确认清空数据表</h3>
                <button class="modal-close" onclick="closeClearDataModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <div class="warning-text">
                        <h4>警告：此操作不可恢复！</h4>
                        <p>您即将清空 <strong>salestable</strong> 数据表中的所有数据。</p>
                        <p>此操作将永久删除所有销售数据记录，且无法撤销。</p>
                        <p>请确认您真的要执行此操作。</p>
                    </div>
                </div>
                <div class="confirmation-input">
                    <label for="confirmText">请输入 <strong>CLEAR</strong> 来确认操作：</label>
                    <input type="text" id="confirmText" placeholder="输入 CLEAR 确认" maxlength="5">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeClearDataModal()">取消</button>
                <button type="button" class="btn btn-danger" id="confirmClearBtn" onclick="confirmClearData()" disabled>
                    <i class="fas fa-trash-alt"></i>
                    确认清空
                </button>
            </div>
        </div>
    </div>

    <script src="js/bubble-3d.js"></script>
    <script src="js/dashboard.js?v=2"></script>
    <script>
        // 简单测试脚本
        console.log('HTML测试脚本执行');

        // 测试销售员表格是否存在
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            const personnelTable = document.getElementById('personnelAnalysisTable');
            const personnelBody = document.getElementById('personnelAnalysisBody');
            console.log('销售员表格元素:', personnelTable);
            console.log('销售员表格body:', personnelBody);

            // 测试是否能找到renderPersonnelAnalysisTable函数
            if (typeof renderPersonnelAnalysisTable === 'function') {
                console.log('找到renderPersonnelAnalysisTable函数');
            } else {
                console.log('未找到renderPersonnelAnalysisTable函数');
            }
        });
    </script>
</body>
</html>
